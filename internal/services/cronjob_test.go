package services

import (
	"os"
	"testing"
	"time"

	"github.com/stefanoschrs/mealpal/internal/config"
	"golang.org/x/oauth2"
)

func TestNewCronjobService(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
	}

	authService := NewAuthService(cfg)
	userStore := NewUserStore()
	sheetsService := NewSheetsService(authService.GetOAuthConfig(), userStore)

	cronjobService := NewCronjobService(userStore, sheetsService, authService)

	if cronjobService == nil {
		t.Fatal("Expected NewCronjobService to return a non-nil service")
	}

	if cronjobService.userStore != userStore {
		t.Error("Expected userStore to be set correctly")
	}

	if cronjobService.sheetsService != sheetsService {
		t.Error("Expected sheetsService to be set correctly")
	}

	if cronjobService.authService != authService {
		t.Error("Expected authService to be set correctly")
	}

	if cronjobService.cron == nil {
		t.Error("Expected cron to be initialized")
	}
}

func TestCronjobService_StartAndStop(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
	}

	authService := NewAuthService(cfg)
	userStore := NewUserStore()
	sheetsService := NewSheetsService(authService.GetOAuthConfig(), userStore)

	cronjobService := NewCronjobService(userStore, sheetsService, authService)

	// Test starting the service
	err := cronjobService.Start()
	if err != nil {
		t.Fatalf("Expected Start() to succeed, got error: %v", err)
	}

	// Give it a moment to start
	time.Sleep(100 * time.Millisecond)

	// Test stopping the service
	cronjobService.Stop()

	// Give it a moment to stop
	time.Sleep(100 * time.Millisecond)
}

func TestCronjobService_CalculateMacroTotals(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
	}

	authService := NewAuthService(cfg)
	userStore := NewUserStore()
	sheetsService := NewSheetsService(authService.GetOAuthConfig(), userStore)

	cronjobService := NewCronjobService(userStore, sheetsService, authService)

	// Test data with proper format
	// Columns: Date, Time, Food Item, Quantity, Calories, Protein, Fat, Carbs, Notes
	testData := [][]interface{}{
		{"2023-01-01", "12:00", "Apple", "1 medium", "80", "0.3", "0.2", "21", "Fresh"},
		{"2023-01-01", "14:00", "Chicken Breast", "100g", "165", "31", "3.6", "0", "Grilled"},
		{"2023-01-01", "18:00", "Rice", "1 cup", "205", "4.3", "0.4", "45", "White rice"},
	}

	calories, protein, fat, carbs, err := cronjobService.CalculateMacroTotals(testData)
	if err != nil {
		t.Fatalf("Expected CalculateMacroTotals to succeed, got error: %v", err)
	}

	// Expected totals: 80 + 165 + 205 = 450 calories
	expectedCalories := 450.0
	if calories != expectedCalories {
		t.Errorf("Expected calories %.1f, got %.1f", expectedCalories, calories)
	}

	// Expected protein: 0.3 + 31 + 4.3 = 35.6g
	expectedProtein := 35.6
	if protein != expectedProtein {
		t.Errorf("Expected protein %.1f, got %.1f", expectedProtein, protein)
	}

	// Expected fat: 0.2 + 3.6 + 0.4 = 4.2g
	expectedFat := 4.2
	if fat != expectedFat {
		t.Errorf("Expected fat %.1f, got %.1f", expectedFat, fat)
	}

	// Expected carbs: 21 + 0 + 45 = 66g
	expectedCarbs := 66.0
	if carbs != expectedCarbs {
		t.Errorf("Expected carbs %.1f, got %.1f", expectedCarbs, carbs)
	}
}

func TestCronjobService_CalculateMacroTotals_EmptyData(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
	}

	authService := NewAuthService(cfg)
	userStore := NewUserStore()
	sheetsService := NewSheetsService(authService.GetOAuthConfig(), userStore)

	cronjobService := NewCronjobService(userStore, sheetsService, authService)

	// Test with empty data
	testData := [][]interface{}{}

	calories, protein, fat, carbs, err := cronjobService.CalculateMacroTotals(testData)
	if err != nil {
		t.Fatalf("Expected CalculateMacroTotals to succeed with empty data, got error: %v", err)
	}

	if calories != 0 {
		t.Errorf("Expected calories 0, got %.1f", calories)
	}
	if protein != 0 {
		t.Errorf("Expected protein 0, got %.1f", protein)
	}
	if fat != 0 {
		t.Errorf("Expected fat 0, got %.1f", fat)
	}
	if carbs != 0 {
		t.Errorf("Expected carbs 0, got %.1f", carbs)
	}
}

func TestCronjobService_CalculateMacroTotals_InvalidData(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
	}

	authService := NewAuthService(cfg)
	userStore := NewUserStore()
	sheetsService := NewSheetsService(authService.GetOAuthConfig(), userStore)

	cronjobService := NewCronjobService(userStore, sheetsService, authService)

	// Test data with invalid/missing values
	testData := [][]interface{}{
		{"2023-01-01", "12:00", "Apple", "1 medium", "invalid", "0.3", "0.2", "21", "Fresh"},
		{"2023-01-01", "14:00", "Chicken Breast", "100g", "165", "", "3.6", "0", "Grilled"},
		{"2023-01-01", "18:00"}, // Insufficient columns
		{"2023-01-01", "20:00", "Rice", "1 cup", "205", "4.3", "0.4", "45", "White rice"},
	}

	calories, protein, fat, carbs, err := cronjobService.CalculateMacroTotals(testData)
	if err != nil {
		t.Fatalf("Expected CalculateMacroTotals to succeed with invalid data, got error: %v", err)
	}

	// Should only count valid entries: 165 + 205 = 370 calories
	expectedCalories := 370.0
	if calories != expectedCalories {
		t.Errorf("Expected calories %.1f, got %.1f", expectedCalories, calories)
	}

	// Should only count valid protein: 0.3 + 4.3 = 4.6g (empty string should be skipped)
	expectedProtein := 4.6
	if protein != expectedProtein {
		t.Errorf("Expected protein %.1f, got %.1f", expectedProtein, protein)
	}

	// Verify fat and carbs are also calculated correctly
	if fat <= 0 {
		t.Errorf("Expected fat to be greater than 0, got %.1f", fat)
	}
	if carbs <= 0 {
		t.Errorf("Expected carbs to be greater than 0, got %.1f", carbs)
	}
}

func TestCronjobService_RefreshTokenIfNeeded(t *testing.T) {
	// Create a temporary directory for testing
	tempDir := t.TempDir()
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
	}

	authService := NewAuthService(cfg)
	userStore := NewUserStore()
	sheetsService := NewSheetsService(authService.GetOAuthConfig(), userStore)

	cronjobService := NewCronjobService(userStore, sheetsService, authService)

	// Test with valid token that doesn't need refresh
	validToken := &oauth2.Token{
		AccessToken:  "valid_token",
		RefreshToken: "refresh_token",
		Expiry:       time.Now().Add(1 * time.Hour), // Expires in 1 hour
	}

	refreshedToken, err := cronjobService.RefreshTokenIfNeeded(validToken)
	if err != nil {
		t.Fatalf("Expected RefreshTokenIfNeeded to succeed with valid token, got error: %v", err)
	}

	if refreshedToken.AccessToken != validToken.AccessToken {
		t.Error("Expected token to remain unchanged when still valid")
	}

	// Test with expired token (this will fail with invalid credentials, but we can test the logic)
	expiredToken := &oauth2.Token{
		AccessToken:  "expired_token",
		RefreshToken: "refresh_token",
		Expiry:       time.Now().Add(-1 * time.Hour), // Expired 1 hour ago
	}

	_, err = cronjobService.RefreshTokenIfNeeded(expiredToken)
	// This should fail because we don't have valid credentials, but that's expected
	if err == nil {
		t.Error("Expected RefreshTokenIfNeeded to fail with invalid credentials")
	}
}
