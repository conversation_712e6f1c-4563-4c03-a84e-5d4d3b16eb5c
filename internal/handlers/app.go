package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/middleware"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

type AppHandler struct {
	foodLogService *services.FoodLogService
	authService    *services.AuthService
	sessionStore   sessions.Store
	cronjobService *services.CronjobService
}

func NewAppHandler(foodLogService *services.FoodLogService, authService *services.AuthService, sessionStore sessions.Store, cronjobService *services.CronjobService) *AppHandler {
	return &AppHandler{
		foodLogService: foodLogService,
		authService:    authService,
		sessionStore:   sessionStore,
		cronjobService: cronjobService,
	}
}

func (h *AppHandler) Home(c *gin.Context) {
	c.HTML(http.StatusOK, "home.html", gin.H{
		"title": "MealPal - Food Logging Assistant",
	})
}

func (h *AppHandler) Dashboard(c *gin.Context) {
	user, exists := h.getCurrentUser(c)
	if !exists {
		c.Redirect(http.StatusFound, "/")
		return
	}

	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "Dashboard - MealPal",
		"user":  user,
	})
}

func (h *AppHandler) SubmitFood(c *gin.Context) {
	user, exists := h.getCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Get OAuth token from context
	tokenInterface, exists := c.Get("oauth_token")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "OAuth token not found",
		})
		return
	}

	token, ok := tokenInterface.(*oauth2.Token)
	if !ok {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Invalid token format",
		})
		return
	}

	// Get food text from form
	foodText := c.PostForm("food_text")
	if foodText == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Food text is required",
		})
		return
	}

	// Process the food entry
	entry, err := h.foodLogService.LogFood(c.Request.Context(), user, token, foodText)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Failed to log food: " + err.Error(),
		})
		return
	}

	// Update session with latest user data (including spreadsheet ID)
	// Get the updated user data after food logging
	updatedUser, exists := h.foodLogService.GetUserStore().GetUser(user.ID)
	if exists {
		err = h.updateUserSession(c, updatedUser.ID, token)
		if err != nil {
			// Log error but don't fail the request
			fmt.Printf("Warning: Failed to update user session: %v\n", err)
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Food logged successfully!",
		"entry":   entry,
	})
}

func (h *AppHandler) GetSpreadsheetInfo(c *gin.Context) {
	user, exists := h.getCurrentUser(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"success": false,
			"error":   "User not authenticated",
		})
		return
	}

	// Check if user has a spreadsheet ID
	if user.SpreadsheetID == "" {
		c.JSON(http.StatusOK, gin.H{
			"success":       true,
			"hasSpreadsheet": false,
		})
		return
	}

	spreadsheetURL := fmt.Sprintf("https://docs.google.com/spreadsheets/d/%s/edit", user.SpreadsheetID)
	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"hasSpreadsheet": true,
		"spreadsheetURL": spreadsheetURL,
	})
}

// TriggerDailySummary manually triggers the daily summary processing (for authenticated users)
func (h *AppHandler) TriggerDailySummary(c *gin.Context) {
	// Trigger the daily summary processing
	go h.cronjobService.RunDailySummaryNow()

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Daily summary processing triggered",
	})
}

// DebugTriggerDailySummary manually triggers daily summary processing (for development/testing - no auth required)
func (h *AppHandler) DebugTriggerDailySummary(c *gin.Context) {
	// Only allow in development mode (check if we're not in production)
	if h.authService.GetOAuthConfig().ClientID != "test_client_id" {
		// In production, require a special debug key
		debugKey := c.GetHeader("X-Debug-Key")
		if debugKey != "mealpal-debug-2025" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Debug endpoint requires X-Debug-Key header",
			})
			return
		}
	}

	// Get date parameter (optional)
	dateParam := c.Query("date")
	var targetDate string

	if dateParam != "" {
		// Validate date format
		if _, err := time.Parse("2006-01-02", dateParam); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "Invalid date format. Use YYYY-MM-DD",
			})
			return
		}
		targetDate = dateParam
	} else {
		// Default to yesterday
		targetDate = time.Now().UTC().AddDate(0, 0, -1).Format("2006-01-02")
	}

	// Get user count for logging
	allUsers := h.foodLogService.GetUserStore().GetAllUsers()

	// Trigger the daily summary processing for specific date
	go h.cronjobService.RunDailySummaryForDate(targetDate)

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"message":     "Daily summary processing triggered via debug endpoint",
		"target_date": targetDate,
		"user_count":  len(allUsers),
		"note":        "Check server logs for processing details",
	})
}

// DebugTriggerDailySummaryWithDate manually triggers daily summary processing for a specific date (path param)
func (h *AppHandler) DebugTriggerDailySummaryWithDate(c *gin.Context) {
	// Only allow in development mode (check if we're not in production)
	if h.authService.GetOAuthConfig().ClientID != "test_client_id" {
		// In production, require a special debug key
		debugKey := c.GetHeader("X-Debug-Key")
		if debugKey != "mealpal-debug-2025" {
			c.JSON(http.StatusForbidden, gin.H{
				"success": false,
				"error":   "Debug endpoint requires X-Debug-Key header",
			})
			return
		}
	}

	// Get date from path parameter
	dateParam := c.Param("date")

	// Validate date format
	if _, err := time.Parse("2006-01-02", dateParam); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid date format. Use YYYY-MM-DD",
		})
		return
	}

	// Get user count for logging
	allUsers := h.foodLogService.GetUserStore().GetAllUsers()

	// Trigger the daily summary processing for specific date
	go h.cronjobService.RunDailySummaryForDate(dateParam)

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"message":     "Daily summary processing triggered via debug endpoint",
		"target_date": dateParam,
		"user_count":  len(allUsers),
		"note":        "Check server logs for processing details",
	})
}

// updateUserSession updates the session with the latest user data from the user store
func (h *AppHandler) updateUserSession(c *gin.Context, userID string, token *oauth2.Token) error {
	// Get the latest user data from the user store
	storedUser, exists := h.foodLogService.GetUserStore().GetUser(userID)
	if !exists {
		return fmt.Errorf("user not found in store")
	}

	// Get current session
	session, err := h.sessionStore.Get(c.Request, middleware.SessionName)
	if err != nil {
		return fmt.Errorf("failed to get session: %w", err)
	}

	// Update session data (this is the old app handler - deprecated)
	sessionData := models.SessionData{
		SessionID:  "deprecated_session",
		IsLoggedIn: true,
	}

	sessionDataJSON, err := json.Marshal(sessionData)
	if err != nil {
		return fmt.Errorf("failed to marshal session data: %w", err)
	}

	session.Values["session_data"] = string(sessionDataJSON)
	return session.Save(c.Request, c.Writer)
}

// getCurrentUser gets the user from context and merges it with the latest data from user store
func (h *AppHandler) getCurrentUser(c *gin.Context) (*models.User, bool) {
	sessionUser, exists := middleware.GetUserFromContext(c)
	if !exists {
		return nil, false
	}

	// Try to get the latest user data from the store
	storedUser, exists := h.foodLogService.GetUserStore().GetUser(sessionUser.ID)
	if exists {
		// Use the stored user data which has the latest spreadsheet ID
		fmt.Printf("DEBUG: Using stored user data for %s, SpreadsheetID: %s\n", storedUser.Email, storedUser.SpreadsheetID)
		return storedUser, true
	}

	// Fall back to session user if not in store yet
	fmt.Printf("DEBUG: Using session user data for %s, SpreadsheetID: %s\n", sessionUser.Email, sessionUser.SpreadsheetID)
	return sessionUser, true
}

func (h *AppHandler) Error(c *gin.Context) {
	errorMsg := c.Query("error")
	if errorMsg == "" {
		errorMsg = "An unknown error occurred"
	}

	c.HTML(http.StatusInternalServerError, "error.html", gin.H{
		"title": "Error - MealPal",
		"error": errorMsg,
	})
}
