package middleware

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/sessions"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

// RedisAuthRequired middleware that uses Redis for session management
func RedisAuthRequired(store sessions.Store, sessionService *services.SessionService, userStore *services.UserStore) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get session from cookie
		session, err := store.Get(c.Request, SessionName)
		if err != nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Get session data from cookie
		sessionDataJSON, ok := session.Values["session_data"].(string)
		if !ok || sessionDataJSON == "" {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		var sessionData models.SessionData
		if err := json.Unmarshal([]byte(sessionDataJSON), &sessionData); err != nil {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		if !sessionData.IsLoggedIn || sessionData.SessionID == "" {
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Get full session data from Redis
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		redisSessionData, err := sessionService.GetSession(ctx, sessionData.SessionID)
		if err != nil {
			// Session not found in Redis, redirect to login
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Get user data from user store
		user, exists := userStore.GetUser(redisSessionData.UserID)
		if !exists {
			// User not found, redirect to login
			c.Redirect(http.StatusFound, "/")
			c.Abort()
			return
		}

		// Create OAuth token from session data
		oauthToken := &oauth2.Token{
			AccessToken:  redisSessionData.AccessToken,
			RefreshToken: redisSessionData.RefreshToken,
			Expiry:       redisSessionData.TokenExpiry,
		}

		// Store user and token in context for handlers to use
		c.Set("user", user)
		c.Set("oauth_token", oauthToken)
		c.Set("session_id", sessionData.SessionID)
		c.Next()
	}
}

// GetSessionIDFromContext retrieves the session ID from the Gin context
func GetSessionIDFromContext(c *gin.Context) (string, bool) {
	sessionID, exists := c.Get("session_id")
	if !exists {
		return "", false
	}
	
	sessionIDStr, ok := sessionID.(string)
	return sessionIDStr, ok
}
