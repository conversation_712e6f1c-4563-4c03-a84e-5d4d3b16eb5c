# MealPal Makefile

# Variables
APP_NAME := mealpal
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go variables
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod

# Build flags
LDFLAGS := -ldflags "-X main.version=$(VERSION) -X main.buildTime=$(BUILD_TIME) -X main.gitCommit=$(GIT_COMMIT)"

# Default target
.PHONY: all
all: clean deps test build

# Help target
.PHONY: help
help: ## Show this help message
	@echo "MealPal - AI Food Logging Assistant"
	@echo ""
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development targets
.PHONY: run
run: generate-version ## Run the application in development mode
	@echo "Starting MealPal in development mode..."
	$(GOCMD) run cmd/main.go

.PHONY: dev
dev: deps run ## Install dependencies and run in development mode

# Build targets
.PHONY: build
build: generate-version ## Build for Linux x64 using goreleaser
	@echo "Building $(APP_NAME) for Linux x64..."
	@if ! command -v goreleaser >/dev/null 2>&1; then \
		echo "Error: goreleaser is not installed. Install it with:"; \
		echo "  brew install goreleaser/tap/goreleaser  # macOS"; \
		echo "  or visit: https://goreleaser.com/install/"; \
		exit 1; \
	fi
	goreleaser build --snapshot --clean --config .goreleaser.yaml
	@echo "✅ Linux binary created: dist/linux_linux_amd64_v1/mealpal"

# Dependency management
.PHONY: deps
deps: ## Download and install dependencies
	@echo "Installing dependencies..."
	$(GOMOD) download
	$(GOMOD) tidy

.PHONY: deps-update
deps-update: ## Update all dependencies
	@echo "Updating dependencies..."
	$(GOGET) -u ./...
	$(GOMOD) tidy

# Testing
.PHONY: test
test: ## Run tests
	@echo "Running tests..."
	$(GOTEST) -v ./cmd/... ./internal/...

.PHONY: test-coverage
test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	$(GOTEST) -v -coverprofile=coverage.out ./cmd/... ./internal/...
	$(GOCMD) tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

# Linting and formatting
.PHONY: fmt
fmt: ## Format Go code
	@echo "Formatting code..."
	$(GOCMD) fmt ./...

.PHONY: vet
vet: ## Run go vet
	@echo "Running go vet..."
	$(GOCMD) vet ./...

.PHONY: lint
lint: fmt vet ## Run formatting and vetting

# Cleaning
.PHONY: clean
clean: ## Clean build artifacts
	@echo "Cleaning..."
	$(GOCLEAN)
	rm -rf bin/
	rm -rf dist/
	rm -f coverage.out coverage.html

# Environment setup
.PHONY: setup
setup: ## Set up development environment
	@echo "Setting up development environment..."
	@if [ ! -f .env ]; then \
		echo "Creating .env file from .env.example..."; \
		cp .env.example .env; \
		echo "Please edit .env file with your API keys"; \
	fi
	$(MAKE) deps
	@echo "Setup complete! Edit .env file with your API keys, then run 'make run'"

# Debug and testing helpers
.PHONY: debug-users
debug-users: ## Show debug information about stored users
	@./scripts/debug-users.sh

.PHONY: reset-data
reset-data: ## Reset all user data (for testing)
	@echo "Resetting all user data..."
	rm -rf data/
	@echo "✅ All user data cleared"

.PHONY: generate-icons
generate-icons: ## Generate PWA icons
	@echo "Generating PWA icons..."
	@./scripts/create-minimal-icons.sh

.PHONY: generate-version
generate-version: ## Generate version file for cache busting
	@echo "Generating version file..."
	@./scripts/generate-version.sh

.PHONY: check-pwa
check-pwa: ## Check PWA requirements
	@echo "🔍 Checking PWA requirements..."
	@echo ""
	@echo "📱 Manifest.json:"
	@if [ -f "static/manifest.json" ]; then \
		echo "  ✅ manifest.json exists"; \
	else \
		echo "  ❌ manifest.json missing"; \
	fi
	@echo ""
	@echo "🔧 Service Worker:"
	@if [ -f "static/js/sw.js" ]; then \
		echo "  ✅ service worker exists"; \
	else \
		echo "  ❌ service worker missing"; \
	fi
	@echo ""
	@echo "🎨 Icons:"
	@for size in 72 96 128 144 152 192 384 512; do \
		if [ -f "static/icons/icon-$${size}x$${size}.png" ]; then \
			echo "  ✅ icon-$${size}x$${size}.png"; \
		else \
			echo "  ❌ icon-$${size}x$${size}.png missing"; \
		fi; \
	done
	@echo ""
	@echo "🌐 HTTPS:"
	@echo "  ⚠️  PWA installation requires HTTPS in production"
	@echo "  ✅ Your site: https://mealpal.stefanoschrs.com"
	@echo ""
	@echo "🧪 Test PWA installation:"
	@echo "  1. Open https://mealpal.stefanoschrs.com on mobile Chrome"
	@echo "  2. Look for 'Add to Home Screen' option"
	@echo "  3. Check Chrome DevTools > Application > Manifest"

# Check if goreleaser is installed
.PHONY: check-goreleaser
check-goreleaser: ## Check if goreleaser is installed
	@if command -v goreleaser >/dev/null 2>&1; then \
		echo "✅ goreleaser is installed: $$(goreleaser --version)"; \
	else \
		echo "❌ goreleaser is not installed"; \
		echo "Install it with:"; \
		echo "  brew install goreleaser/tap/goreleaser  # macOS"; \
		echo "  or visit: https://goreleaser.com/install/"; \
	fi

deploy:
	@rsync -rlvz --checksum static templates dist/linux_linux_amd64_v1/mealpal <EMAIL>:/apps/mealpal/
