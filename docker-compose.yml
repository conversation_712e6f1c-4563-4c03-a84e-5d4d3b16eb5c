version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: mealpal-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --requirepass ${REDIS_PASSWORD}
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - mealpal-network

volumes:
  redis_data:
    driver: local

networks:
  mealpal-network:
    driver: bridge
