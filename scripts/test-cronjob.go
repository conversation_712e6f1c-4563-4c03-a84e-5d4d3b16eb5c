package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"github.com/stefanoschrs/mealpal/internal/config"
	"github.com/stefanoschrs/mealpal/internal/models"
	"github.com/stefanoschrs/mealpal/internal/services"
	"golang.org/x/oauth2"
)

func main() {
	fmt.Println("🧪 Testing MealPal Cronjob Functionality")
	fmt.Println("========================================")

	// Create a temporary directory for testing
	tempDir := "/tmp/mealpal-test"
	os.RemoveAll(tempDir)
	os.MkdirAll(tempDir, 0755)
	defer os.RemoveAll(tempDir)

	// Change to temp directory
	originalDir, _ := os.Getwd()
	os.Chdir(tempDir)
	defer os.Chdir(originalDir)

	// Create test configuration
	cfg := &config.Config{
		GoogleClientID:     "test_client_id",
		GoogleClientSecret: "test_client_secret",
		GoogleRedirectURL:  "http://localhost:8080/auth/google/callback",
		GeminiAPIKey:       "test_gemini_key",
		SessionSecret:      "test_session_secret",
	}

	// Initialize services
	authService := services.NewAuthService(cfg)
	userStore := services.NewUserStore()
	sheetsService := services.NewSheetsService(authService.GetOAuthConfig(), userStore)
	cronjobService := services.NewCronjobService(userStore, sheetsService, authService)

	fmt.Println("✅ Services initialized successfully")

	// Test 1: Create test users
	fmt.Println("\n📝 Test 1: Creating test users...")
	
	user1 := &models.User{
		ID:            "test_user_1",
		Email:         "<EMAIL>",
		Name:          "Test User 1",
		AccessToken:   "fake_access_token_1",
		RefreshToken:  "fake_refresh_token_1",
		SpreadsheetID: "fake_spreadsheet_1",
	}
	
	user2 := &models.User{
		ID:            "test_user_2",
		Email:         "<EMAIL>",
		Name:          "Test User 2",
		AccessToken:   "fake_access_token_2",
		RefreshToken:  "fake_refresh_token_2",
		SpreadsheetID: "fake_spreadsheet_2",
	}

	userStore.SaveUser(user1)
	userStore.SaveUser(user2)

	fmt.Printf("✅ Created 2 test users\n")

	// Test 2: Verify GetAllUsers works
	fmt.Println("\n📋 Test 2: Testing GetAllUsers...")
	
	allUsers := userStore.GetAllUsers()
	fmt.Printf("✅ Retrieved %d users from store\n", len(allUsers))
	
	for _, user := range allUsers {
		fmt.Printf("   - %s (%s)\n", user.Name, user.Email)
	}

	// Test 3: Test macro calculation
	fmt.Println("\n🧮 Test 3: Testing macro calculation...")
	
	testData := [][]interface{}{
		{"2023-01-01", "12:00", "Apple", "1 medium", "80", "0.3", "0.2", "21", "Fresh"},
		{"2023-01-01", "14:00", "Chicken Breast", "100g", "165", "31", "3.6", "0", "Grilled"},
		{"2023-01-01", "18:00", "Rice", "1 cup", "205", "4.3", "0.4", "45", "White rice"},
	}

	calories, protein, fat, carbs, err := cronjobService.CalculateMacroTotals(testData)
	if err != nil {
		log.Fatalf("❌ Failed to calculate macros: %v", err)
	}

	fmt.Printf("✅ Calculated totals:\n")
	fmt.Printf("   - Calories: %.1f\n", calories)
	fmt.Printf("   - Protein: %.1f g\n", protein)
	fmt.Printf("   - Fat: %.1f g\n", fat)
	fmt.Printf("   - Carbs: %.1f g\n", carbs)

	// Expected: 450 calories, 35.6g protein, 4.2g fat, 66g carbs
	if calories != 450.0 {
		log.Fatalf("❌ Expected 450 calories, got %.1f", calories)
	}
	if protein != 35.6 {
		log.Fatalf("❌ Expected 35.6g protein, got %.1f", protein)
	}

	// Test 4: Test cronjob service start/stop
	fmt.Println("\n⏰ Test 4: Testing cronjob service start/stop...")
	
	err = cronjobService.Start()
	if err != nil {
		log.Fatalf("❌ Failed to start cronjob service: %v", err)
	}
	fmt.Println("✅ Cronjob service started successfully")

	// Wait a moment
	time.Sleep(100 * time.Millisecond)

	cronjobService.Stop()
	fmt.Println("✅ Cronjob service stopped successfully")

	// Test 5: Test token refresh logic
	fmt.Println("\n🔄 Test 5: Testing token refresh logic...")
	
	// Test with valid token
	validToken := &oauth2.Token{
		AccessToken:  "valid_token",
		RefreshToken: "refresh_token",
		Expiry:       time.Now().Add(1 * time.Hour),
	}

	refreshedToken, err := cronjobService.RefreshTokenIfNeeded(validToken)
	if err != nil {
		log.Fatalf("❌ Failed to handle valid token: %v", err)
	}

	if refreshedToken.AccessToken != validToken.AccessToken {
		log.Fatalf("❌ Token should not have changed for valid token")
	}
	fmt.Println("✅ Valid token handling works correctly")

	// Test with expired token (will fail with fake credentials, but that's expected)
	expiredToken := &oauth2.Token{
		AccessToken:  "expired_token",
		RefreshToken: "refresh_token",
		Expiry:       time.Now().Add(-1 * time.Hour),
	}

	_, err = cronjobService.RefreshTokenIfNeeded(expiredToken)
	if err == nil {
		log.Fatalf("❌ Expected token refresh to fail with fake credentials")
	}
	fmt.Println("✅ Expired token handling works correctly (expected failure)")

	fmt.Println("\n🎉 All tests passed!")
	fmt.Println("✅ Cronjob functionality is working correctly")
	fmt.Println("✅ Ready for production deployment")
}
